{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/novels/author/route": "app/api/novels/author/route.js", "/api/novels/route": "app/api/novels/route.js", "/api/credits/balance/route": "app/api/credits/balance/route.js", "/page": "app/page.js", "/_not-found/page": "app/_not-found/page.js", "/auth/error/page": "app/auth/error/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/browse/page": "app/browse/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js"}