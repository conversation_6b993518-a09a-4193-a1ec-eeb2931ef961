/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/author/route";
exports.ids = ["app/api/novels/author/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Fauthor%2Froute&page=%2Fapi%2Fnovels%2Fauthor%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Fauthor%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Fauthor%2Froute&page=%2Fapi%2Fnovels%2Fauthor%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Fauthor%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_author_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/author/route.ts */ \"(rsc)/./src/app/api/novels/author/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/author/route\",\n        pathname: \"/api/novels/author\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/author/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/author/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_author_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/author/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZub3ZlbHMlMkZhdXRob3IlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRm5vdmVscyUyRmF1dGhvciUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRm5vdmVscyUyRmF1dGhvciUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRndlZXJhd2F0JTJGRGVza3RvcCUyRmFkYy1wbGF0Zm9ybSUyRnNlcnZpY2VzJTJGY29udGVudCUyRmJsYWNrLWJsb2clMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGd2VlcmF3YXQlMkZEZXNrdG9wJTJGYWRjLXBsYXRmb3JtJTJGc2VydmljZXMlMkZjb250ZW50JTJGYmxhY2stYmxvZyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDbUQ7QUFDaEk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLz84ZWVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL2F1dGhvci9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbm92ZWxzL2F1dGhvci9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL25vdmVscy9hdXRob3JcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL25vdmVscy9hdXRob3Ivcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvd2VlcmF3YXQvRGVza3RvcC9hZGMtcGxhdGZvcm0vc2VydmljZXMvY29udGVudC9ibGFjay1ibG9nL3NyYy9hcHAvYXBpL25vdmVscy9hdXRob3Ivcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL25vdmVscy9hdXRob3Ivcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Fauthor%2Froute&page=%2Fapi%2Fnovels%2Fauthor%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Fauthor%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/author/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/novels/author/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types/auth */ \"(rsc)/./src/types/auth.ts\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n        const status = searchParams.get(\"status\") // Optional status filter\n        ;\n        const search = searchParams.get(\"search\") // Optional search term\n        ;\n        const skip = (page - 1) * limit;\n        // Build the base query for author's novels\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).eq(\"author_id\", session.user.id).order(\"updated_at\", {\n            ascending: false\n        }).range(skip, skip + limit - 1);\n        // Add status filter if provided\n        if (status && Object.values(_types_auth__WEBPACK_IMPORTED_MODULE_4__.NovelStatus).includes(status)) {\n            query = query.eq(\"status\", status);\n        }\n        // Add search filter if provided\n        if (search && search.trim()) {\n            query = query.or(`title.ilike.%${search.trim()}%,description.ilike.%${search.trim()}%,synopsis.ilike.%${search.trim()}%`);\n        }\n        // Execute the query\n        const { data: novels, error: novelsError } = await query;\n        if (novelsError) {\n            console.error(\"Error fetching author novels:\", novelsError);\n            throw novelsError;\n        }\n        // Get total count for pagination\n        let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"author_id\", session.user.id);\n        if (status && Object.values(_types_auth__WEBPACK_IMPORTED_MODULE_4__.NovelStatus).includes(status)) {\n            countQuery = countQuery.eq(\"status\", status);\n        }\n        if (search && search.trim()) {\n            countQuery = countQuery.or(`title.ilike.%${search.trim()}%,description.ilike.%${search.trim()}%,synopsis.ilike.%${search.trim()}%`);\n        }\n        const { count: total, error: countError } = await countQuery;\n        if (countError) {\n            console.error(\"Error counting author novels:\", countError);\n            throw countError;\n        }\n        // Get chapter counts for each novel\n        const novelIds = novels?.map((novel)=>novel.id) || [];\n        let chapterCounts = {};\n        let libraryCounts = {};\n        if (novelIds.length > 0) {\n            // Get chapter counts\n            const { data: chapters } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"novel_id\").in(\"novel_id\", novelIds);\n            if (chapters) {\n                chapterCounts = chapters.reduce((acc, chapter)=>{\n                    acc[chapter.novel_id] = (acc[chapter.novel_id] || 0) + 1;\n                    return acc;\n                }, {});\n            }\n            // Get library counts (followers)\n            const { data: libraryEntries } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"library\").select(\"novel_id\").in(\"novel_id\", novelIds);\n            if (libraryEntries) {\n                libraryCounts = libraryEntries.reduce((acc, entry)=>{\n                    acc[entry.novel_id] = (acc[entry.novel_id] || 0) + 1;\n                    return acc;\n                }, {});\n            }\n        }\n        // Transform novels to match expected format\n        const transformedNovels = novels?.map((novel)=>({\n                ...novel,\n                // Convert snake_case to camelCase for consistency with frontend\n                createdAt: novel.created_at,\n                updatedAt: novel.updated_at,\n                authorId: novel.author_id,\n                // Add author data from join\n                author: novel.users ? {\n                    id: novel.users.id,\n                    name: novel.users.name || \"Unknown\",\n                    image: novel.users.image\n                } : {\n                    id: novel.author_id,\n                    name: \"Unknown\",\n                    image: null\n                },\n                _count: {\n                    chapters: chapterCounts[novel.id] || 0,\n                    library: libraryCounts[novel.id] || 0\n                }\n            })) || [];\n        // Calculate additional statistics\n        const [publishedCount, draftCount, totalChaptersCount, totalFollowersCount] = await Promise.all([\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"author_id\", session.user.id).eq(\"status\", _types_auth__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.PUBLISHED),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"author_id\", session.user.id).eq(\"status\", _types_auth__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.DRAFT),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).in(\"novel_id\", novelIds),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"library\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).in(\"novel_id\", novelIds)\n        ]);\n        const stats = {\n            total: total || 0,\n            published: publishedCount.count || 0,\n            draft: draftCount.count || 0,\n            totalChapters: totalChaptersCount.count || 0,\n            totalFollowers: totalFollowersCount.count || 0\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels: transformedNovels,\n            pagination: {\n                page,\n                limit,\n                total: total || 0,\n                pages: Math.ceil((total || 0) / limit),\n                hasNext: page * limit < (total || 0),\n                hasPrev: page > 1\n            },\n            stats\n        });\n    } catch (error) {\n        console.error(\"Error fetching author novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch author novels\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/author/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/auth.ts":
/*!***************************!*\
  !*** ./src/types/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChapterStatus: () => (/* binding */ ChapterStatus),\n/* harmony export */   NovelStatus: () => (/* binding */ NovelStatus),\n/* harmony export */   PaymentStatus: () => (/* binding */ PaymentStatus),\n/* harmony export */   SubscriptionStatus: () => (/* binding */ SubscriptionStatus),\n/* harmony export */   SubscriptionTier: () => (/* binding */ SubscriptionTier),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   hasAdminAccess: () => (/* binding */ hasAdminAccess),\n/* harmony export */   hasAuthorAccess: () => (/* binding */ hasAuthorAccess),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isAuthor: () => (/* binding */ isAuthor),\n/* harmony export */   isReader: () => (/* binding */ isReader)\n/* harmony export */ });\n// Authentication and authorization types\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"READER\"] = \"READER\";\n    UserRole[\"AUTHOR\"] = \"AUTHOR\";\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n})(UserRole || (UserRole = {}));\nvar SubscriptionTier;\n(function(SubscriptionTier) {\n    SubscriptionTier[\"FREE\"] = \"FREE\";\n    SubscriptionTier[\"PREMIUM\"] = \"PREMIUM\";\n    SubscriptionTier[\"PREMIUM_PLUS\"] = \"PREMIUM_PLUS\";\n})(SubscriptionTier || (SubscriptionTier = {}));\nvar SubscriptionStatus;\n(function(SubscriptionStatus) {\n    SubscriptionStatus[\"ACTIVE\"] = \"ACTIVE\";\n    SubscriptionStatus[\"CANCELED\"] = \"CANCELED\";\n    SubscriptionStatus[\"PAST_DUE\"] = \"PAST_DUE\";\n    SubscriptionStatus[\"UNPAID\"] = \"UNPAID\";\n    SubscriptionStatus[\"INCOMPLETE\"] = \"INCOMPLETE\";\n    SubscriptionStatus[\"INCOMPLETE_EXPIRED\"] = \"INCOMPLETE_EXPIRED\";\n    SubscriptionStatus[\"TRIALING\"] = \"TRIALING\";\n})(SubscriptionStatus || (SubscriptionStatus = {}));\nvar PaymentStatus;\n(function(PaymentStatus) {\n    PaymentStatus[\"PENDING\"] = \"PENDING\";\n    PaymentStatus[\"COMPLETED\"] = \"COMPLETED\";\n    PaymentStatus[\"FAILED\"] = \"FAILED\";\n    PaymentStatus[\"CANCELED\"] = \"CANCELED\";\n    PaymentStatus[\"REFUNDED\"] = \"REFUNDED\";\n})(PaymentStatus || (PaymentStatus = {}));\nvar NovelStatus;\n(function(NovelStatus) {\n    NovelStatus[\"DRAFT\"] = \"DRAFT\";\n    NovelStatus[\"PUBLISHED\"] = \"PUBLISHED\";\n    NovelStatus[\"COMPLETED\"] = \"COMPLETED\";\n    NovelStatus[\"ARCHIVED\"] = \"ARCHIVED\";\n})(NovelStatus || (NovelStatus = {}));\nvar ChapterStatus;\n(function(ChapterStatus) {\n    ChapterStatus[\"DRAFT\"] = \"DRAFT\";\n    ChapterStatus[\"PUBLISHED\"] = \"PUBLISHED\";\n})(ChapterStatus || (ChapterStatus = {}));\n// Helper functions for role checking\nfunction isAuthor(role) {\n    return role === \"AUTHOR\";\n}\nfunction isReader(role) {\n    return role === \"READER\";\n}\nfunction isAdmin(role) {\n    return role === \"ADMIN\";\n}\nfunction hasAuthorAccess(role) {\n    return role === \"AUTHOR\" || role === \"ADMIN\";\n}\nfunction hasAdminAccess(role) {\n    return role === \"ADMIN\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Fauthor%2Froute&page=%2Fapi%2Fnovels%2Fauthor%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Fauthor%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();