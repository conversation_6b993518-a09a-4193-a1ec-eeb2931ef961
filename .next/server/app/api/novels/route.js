/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/route";
exports.ids = ["app/api/novels/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/route.ts */ \"(rsc)/./src/app/api/novels/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/route\",\n        pathname: \"/api/novels\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/novels/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n    const genre = searchParams.get(\"genre\");\n    const search = searchParams.get(\"search\");\n    const status = searchParams.get(\"status\") || \"PUBLISHED\";\n    const sortBy = searchParams.get(\"sortBy\") || \"created_at\";\n    const sortOrder = searchParams.get(\"sortOrder\") === \"asc\" ? \"asc\" : \"desc\";\n    const tags = searchParams.get(\"tags\")?.split(\",\").filter(Boolean) || [];\n    const skip = (page - 1) * limit;\n    try {\n        // Build the base query for novels with author information\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).eq(\"status\", status).range(skip, skip + limit - 1);\n        // Add genre filter if provided\n        if (genre) {\n            query = query.eq(\"genre\", genre);\n        }\n        // Add tags filter if provided (novels that contain any of the specified tags)\n        if (tags.length > 0) {\n            query = query.overlaps(\"tags\", tags);\n        }\n        // Add search filter if provided\n        if (search) {\n            query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`);\n        }\n        // Add sorting\n        const sortColumn = sortBy === \"author\" ? \"users.name\" : sortBy;\n        query = query.order(sortColumn, {\n            ascending: sortOrder === \"asc\"\n        });\n        const { data: novels, error: novelsError } = await query;\n        if (novelsError) {\n            console.error(\"Error fetching novels:\", novelsError);\n            throw novelsError;\n        }\n        // Get total count for pagination with same filters\n        let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"status\", status);\n        if (genre) {\n            countQuery = countQuery.eq(\"genre\", genre);\n        }\n        if (tags.length > 0) {\n            countQuery = countQuery.overlaps(\"tags\", tags);\n        }\n        if (search) {\n            countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%,synopsis.ilike.%${search}%`);\n        }\n        const { count: total, error: countError } = await countQuery;\n        if (countError) {\n            console.error(\"Error counting novels:\", countError);\n            throw countError;\n        }\n        // Get chapter counts for each novel\n        const novelIds = novels?.map((novel)=>novel.id) || [];\n        let chapterCounts = {};\n        if (novelIds.length > 0) {\n            const { data: chapters, error: chaptersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"novel_id\").in(\"novel_id\", novelIds).eq(\"status\", \"PUBLISHED\");\n            if (!chaptersError && chapters) {\n                chapterCounts = chapters.reduce((acc, chapter)=>{\n                    acc[chapter.novel_id] = (acc[chapter.novel_id] || 0) + 1;\n                    return acc;\n                }, {});\n            }\n        }\n        // Transform snake_case to camelCase and add missing fields\n        const transformedNovels = novels?.map((novel)=>({\n                ...novel,\n                // Convert snake_case to camelCase for consistency with frontend\n                createdAt: novel.created_at,\n                updatedAt: novel.updated_at,\n                authorId: novel.author_id,\n                // Add author data from join\n                author: novel.users ? {\n                    id: novel.users.id,\n                    name: novel.users.name || \"Unknown\",\n                    image: novel.users.image\n                } : {\n                    id: novel.author_id,\n                    name: \"Unknown\",\n                    image: null\n                },\n                _count: {\n                    chapters: chapterCounts[novel.id] || 0\n                }\n            })) || [];\n        // Get available genres for faceted search\n        const { data: genreData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"genre\").eq(\"status\", status).not(\"genre\", \"is\", null);\n        const genreCounts = genreData?.reduce((acc, novel)=>{\n            if (novel.genre) {\n                acc[novel.genre] = (acc[novel.genre] || 0) + 1;\n            }\n            return acc;\n        }, {}) || {};\n        const availableGenres = Object.entries(genreCounts).map(([genre, count])=>({\n                genre,\n                count\n            })).sort((a, b)=>b.count - a.count);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels: transformedNovels,\n            pagination: {\n                page,\n                limit,\n                total: total || 0,\n                pages: Math.ceil((total || 0) / limit),\n                hasNext: page * limit < (total || 0),\n                hasPrev: page > 1\n            },\n            filters: {\n                search,\n                genre,\n                status,\n                tags,\n                sortBy,\n                sortOrder\n            },\n            facets: {\n                genres: availableGenres\n            },\n            metadata: {\n                searchTime: Date.now(),\n                totalResults: total || 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch novels\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags } = body;\n        // Validate required fields\n        if (!title || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Ensure user exists in database (create if not exists)\n        const { data: existingUser } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"users\").select(\"id\").eq(\"id\", session.user.id).single();\n        if (!existingUser) {\n            // Create user in database if they don't exist\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"users\").insert({\n                id: session.user.id,\n                email: session.user.email,\n                name: session.user.name,\n                image: session.user.image,\n                role: session.user.role || \"READER\",\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            });\n            if (userError) {\n                console.error(\"Error creating user:\", userError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to create user\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // Generate a unique ID for the novel (similar to CUID format)\n        const novelId = `cm${(0,crypto__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(12).toString(\"base64url\")}`;\n        const now = new Date().toISOString();\n        const { data: novel, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").insert({\n            id: novelId,\n            title: title.trim(),\n            description: description?.trim() || null,\n            synopsis: synopsis?.trim() || null,\n            genre: genre?.trim() || null,\n            tags: tags || [],\n            author_id: session.user.id,\n            status: \"DRAFT\",\n            created_at: now,\n            updated_at: now\n        }).select(`\n        *,\n        users!novels_author_id_fkey(id, name, image)\n      `).single();\n        if (error) {\n            console.error(\"Error creating novel:\", error);\n            throw error;\n        }\n        // Transform the response to match frontend expectations\n        const novelWithCount = {\n            ...novel,\n            // Convert snake_case to camelCase for consistency with frontend\n            createdAt: novel.created_at,\n            updatedAt: novel.updated_at,\n            authorId: novel.author_id,\n            // Add author data from join\n            author: novel.users ? {\n                id: novel.users.id,\n                name: novel.users.name || \"Unknown\",\n                image: novel.users.image\n            } : {\n                id: novel.author_id,\n                name: \"Unknown\",\n                image: null\n            },\n            _count: {\n                chapters: 0\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novelWithCount, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error creating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Froute&page=%2Fapi%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();